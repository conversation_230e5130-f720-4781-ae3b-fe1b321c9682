## Component responsible for managing entity health and damage reception
## Implements IDamageable interface following Dependency Inversion Principle
## Handles health, death, and damage processing with proper signal emission
class_name HealthComponent
extends Node

signal health_changed(current_health: float, max_health: float, percentage: float)
signal damage_taken(amount: float, damage_type: int, is_critical: bool)
signal healed(amount: float)
signal died
signal revived

@export var invincibility_duration: float = 0.5
@export var death_destroys_entity: bool = false

var current_health: float
var is_dead: bool = false
var is_invincible: bool = false
var _invincibility_timer: float = 0.0

# Component dependencies
var _stats_component: StatsComponent
var _buff_component: BuffComponent

func _ready() -> void:
	_initialize_components()
	_initialize_health()

func _process(delta: float) -> void:
	_update_invincibility(delta)

## Implements IDamageable interface
func take_damage(amount: float, source: Node, damage_type: int, is_critical: bool = false) -> void:
	if not can_take_damage():
		return
	
	var final_damage: float = _calculate_damage_reduction(amount, damage_type)
	_apply_damage(final_damage, damage_type, is_critical)
	_trigger_invincibility()

## Implements IDamageable interface
func can_take_damage() -> bool:
	return not is_dead and not is_invincible

## Implements IDamageable interface
func get_health_percentage() -> float:
	if not _stats_component:
		return 0.0
	var max_hp: float = _stats_component.get_max_health()
	return current_health / max_hp if max_hp > 0 else 0.0

## Heals the entity by the specified amount
func heal(amount: float) -> void:
	if is_dead:
		return
	
	var max_hp: float = _stats_component.get_max_health() if _stats_component else 100.0
	var old_health: float = current_health
	current_health = min(current_health + amount, max_hp)
	
	var actual_heal: float = current_health - old_health
	if actual_heal > 0:
		emit_signal("healed", actual_heal)
		_emit_health_changed()

## Instantly kills the entity
func kill() -> void:
	if is_dead:
		return
	
	current_health = 0.0
	_handle_death()

## Revives the entity with specified health percentage
func revive(health_percentage: float = 1.0) -> void:
	if not is_dead:
		return
	
	var max_hp: float = _stats_component.get_max_health() if _stats_component else 100.0
	current_health = max_hp * clamp(health_percentage, 0.0, 1.0)
	is_dead = false
	emit_signal("revived")
	_emit_health_changed()

## Returns current health value
func get_current_health() -> float:
	return current_health

## Returns maximum health value
func get_max_health() -> float:
	return _stats_component.get_max_health() if _stats_component else 100.0

## Initializes component dependencies
func _initialize_components() -> void:
	_stats_component = get_parent().get_node_or_null("StatsComponent") as StatsComponent
	_buff_component = get_parent().get_node_or_null("BuffComponent") as BuffComponent
	
	if not _stats_component:
		push_warning("HealthComponent: No StatsComponent found on parent")

## Initializes health to maximum value
func _initialize_health() -> void:
	var max_hp: float = _stats_component.get_max_health() if _stats_component else 100.0
	current_health = max_hp
	_emit_health_changed()

## Calculates damage after resistances and armor
func _calculate_damage_reduction(base_damage: float, damage_type: int) -> float:
	var final_damage: float = base_damage
	
	# Apply damage type resistance
	if _stats_component:
		var resistance: float = _stats_component.get_damage_resistance(damage_type)
		final_damage *= (1.0 - resistance)
		
		# Apply armor reduction (only for physical damage)
		if damage_type == 0:  # 0 = PHYSICAL
			var armor_reduction: float = _stats_component.get_armor_reduction()
			final_damage *= (1.0 - armor_reduction)
	
	# Apply buff modifiers
	if _buff_component:
		var damage_reduction_multiplier: float = _buff_component.get_stat_multiplier("damage_reduction")
		final_damage *= (1.0 - damage_reduction_multiplier)
	
	return max(final_damage, 0.0)

## Applies damage and handles death
func _apply_damage(damage: float, damage_type: int, is_critical: bool) -> void:
	current_health = max(current_health - damage, 0.0)
	emit_signal("damage_taken", damage, damage_type, is_critical)
	_emit_health_changed()
	
	if current_health <= 0.0:
		_handle_death()

## Handles entity death
func _handle_death() -> void:
	if is_dead:
		return
	
	is_dead = true
	emit_signal("died")
	
	if death_destroys_entity:
		get_parent().queue_free()

## Triggers invincibility frames
func _trigger_invincibility() -> void:
	if invincibility_duration > 0.0:
		is_invincible = true
		_invincibility_timer = invincibility_duration

## Updates invincibility timer
func _update_invincibility(delta: float) -> void:
	if is_invincible:
		_invincibility_timer -= delta
		if _invincibility_timer <= 0.0:
			is_invincible = false

## Emits health changed signal with current values
func _emit_health_changed() -> void:
	var max_hp: float = get_max_health()
	var percentage: float = get_health_percentage()
	emit_signal("health_changed", current_health, max_hp, percentage)
