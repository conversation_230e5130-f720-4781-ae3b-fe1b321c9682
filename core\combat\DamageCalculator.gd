## Static utility class for calculating damage between entities
## Follows Single Responsibility Principle - only handles damage calculations
## Uses dependency injection to work with any entities that have required components
extends Node

## Result structure for damage calculations
class DamageResult:
	var damage: float
	var is_critical: bool
	var hit: bool
	var damage_type: DamageType.Type

	func _init(dmg: float, crit: bool, did_hit: bool, type: DamageType.Type) -> void:
		damage = dmg
		is_critical = crit
		hit = did_hit
		damage_type = type

## Calculates damage between attacker and target
## Returns a DamageResult with all relevant information
static func calculate_damage(attacker: Node, target: Node, damage_type: DamageType.Type = DamageType.Type.PHYSICAL) -> DamageResult:
	# Get required components
	var attacker_stats: StatsComponent = attacker.get_node_or_null("StatsComponent")
	var attacker_buffs: BuffComponent = attacker.get_node_or_null("BuffComponent")
	var attacker_items: ItemComponent = attacker.get_node_or_null("ItemComponent")
	
	var target_stats: StatsComponent = target.get_node_or_null("StatsComponent")
	var target_buffs: BuffComponent = target.get_node_or_null("BuffComponent")
	var target_items: ItemComponent = target.get_node_or_null("ItemComponent")
	
	# Check if hit connects
	var hit_result: bool = _calculate_hit_chance(attacker_stats, target_stats, attacker_buffs, target_buffs)
	if not hit_result:
		return DamageResult.new(0.0, false, false, damage_type)
	
	# Calculate base damage
	var base_damage: float = _calculate_base_damage(attacker_stats, attacker_items, attacker_buffs)
	
	# Check for critical hit
	var is_critical: bool = _calculate_critical_hit(attacker_stats, attacker_items, attacker_buffs)
	if is_critical:
		base_damage *= _calculate_critical_multiplier(attacker_stats, attacker_items, attacker_buffs)
	
	# Apply defensive calculations
	var final_damage: float = _apply_defensive_calculations(
		base_damage,
		damage_type,
		target_stats,
		target_items,
		target_buffs
	)
	
	return DamageResult.new(final_damage, is_critical, true, damage_type)

## Calculates whether an attack hits the target
static func _calculate_hit_chance(
	attacker_stats: StatsComponent,
	target_stats: StatsComponent,
	attacker_buffs: BuffComponent,
	target_buffs: BuffComponent
) -> bool:
	var base_hit_chance: float = 0.95  # 95% base hit chance
	
	# Attacker accuracy bonuses
	if attacker_stats:
		var accuracy_bonus: float = attacker_stats.dexterity * 0.001  # 0.1% per dexterity point
		base_hit_chance += accuracy_bonus
	
	if attacker_buffs:
		base_hit_chance *= attacker_buffs.get_stat_multiplier("accuracy")
	
	# Target evasion
	if target_stats:
		var evasion_bonus: float = target_stats.dexterity * 0.0005  # 0.05% per dexterity point
		base_hit_chance -= evasion_bonus
	
	if target_buffs:
		base_hit_chance *= (1.0 - target_buffs.get_stat_multiplier("evasion"))
	
	# Clamp between 5% and 95%
	base_hit_chance = clamp(base_hit_chance, 0.05, 0.95)
	
	return randf() < base_hit_chance

## Calculates the base damage before modifiers
static func _calculate_base_damage(
	attacker_stats: StatsComponent,
	attacker_items: ItemComponent,
	attacker_buffs: BuffComponent
) -> float:
	var base_damage: float = 0.0
	
	# Base attack from stats
	if attacker_stats:
		base_damage = attacker_stats.get_total_attack()
	
	# Weapon damage
	if attacker_items:
		base_damage += attacker_items.get_total_attack_bonus()
	
	# Buff modifiers
	if attacker_buffs:
		base_damage *= attacker_buffs.get_stat_multiplier("attack")
		base_damage += attacker_buffs.get_stat_flat_bonus("attack")
	
	# Add some randomness (±10%)
	var damage_variance: float = base_damage * 0.1
	base_damage += randf_range(-damage_variance, damage_variance)
	
	return max(base_damage, 1.0)  # Minimum 1 damage

## Determines if an attack is a critical hit
static func _calculate_critical_hit(
	attacker_stats: StatsComponent,
	attacker_items: ItemComponent,
	attacker_buffs: BuffComponent
) -> bool:
	var crit_chance: float = 0.0
	
	# Base critical chance from stats
	if attacker_stats:
		crit_chance = attacker_stats.critical_chance
	
	# Weapon critical chance bonus
	if attacker_items:
		crit_chance += attacker_items.get_critical_chance_bonus()
	
	# Buff modifiers
	if attacker_buffs:
		crit_chance *= attacker_buffs.get_stat_multiplier("critical_chance")
		crit_chance += attacker_buffs.get_stat_flat_bonus("critical_chance")
	
	# Cap at 50% critical chance
	crit_chance = min(crit_chance, 0.5)
	
	return randf() < crit_chance

## Calculates the critical hit damage multiplier
static func _calculate_critical_multiplier(
	attacker_stats: StatsComponent,
	attacker_items: ItemComponent,
	attacker_buffs: BuffComponent
) -> float:
	var crit_multiplier: float = 1.5  # Base 150% damage
	
	# Stats critical multiplier
	if attacker_stats:
		crit_multiplier = attacker_stats.critical_multiplier
	
	# Weapon critical damage bonus
	if attacker_items:
		crit_multiplier += attacker_items.get_critical_damage_bonus()
	
	# Buff modifiers
	if attacker_buffs:
		crit_multiplier *= attacker_buffs.get_stat_multiplier("critical_damage")
		crit_multiplier += attacker_buffs.get_stat_flat_bonus("critical_damage")
	
	return max(crit_multiplier, 1.0)  # Minimum 100% damage

## Applies defensive calculations (armor, resistances, etc.)
static func _apply_defensive_calculations(
	base_damage: float,
	damage_type: DamageType.Type,
	target_stats: StatsComponent,
	target_items: ItemComponent,
	target_buffs: BuffComponent
) -> float:
	var final_damage: float = base_damage
	
	# Apply armor reduction (only for physical damage)
	if damage_type == DamageType.Type.PHYSICAL and target_stats:
		var armor_reduction: float = target_stats.get_armor_reduction()
		
		# Add armor from items
		if target_items:
			var item_armor: float = target_items.get_total_armor_bonus()
			var total_armor: float = target_stats.get_total_armor() + item_armor
			armor_reduction = total_armor / (total_armor + 100.0)
		
		final_damage *= (1.0 - armor_reduction)
	
	# Apply damage type resistance
	var total_resistance: float = 0.0
	
	if target_stats:
		total_resistance += target_stats.get_damage_resistance(damage_type)
	
	if target_items:
		total_resistance += target_items.get_total_resistance(damage_type)
	
	# Apply buff-based resistances
	if target_buffs:
		var resistance_key: String = "resistance_" + DamageType.get_type_name(damage_type).to_lower()
		total_resistance += target_buffs.get_stat_flat_bonus(resistance_key)
		total_resistance *= target_buffs.get_stat_multiplier("damage_resistance")
	
	# Cap total resistance at 95%
	total_resistance = min(total_resistance, 0.95)
	final_damage *= (1.0 - total_resistance)
	
	return max(final_damage, 0.0)
