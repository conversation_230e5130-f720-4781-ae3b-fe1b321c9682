## Test controller for demonstrating the damage calculation system
## Provides interactive testing of all combat and inventory features
## Follows clean code practices with clear separation of concerns
extends Node3D

@onready var player: CharacterBody3D = $Player
@onready var enemy: CharacterBody3D = $Enemy
@onready var damage_display: DamageDisplay = $UI/DamageDisplay
@onready var player_health_bar: HealthBar = $UI/PlayerHealthBar
@onready var enemy_health_bar: HealthBar = $UI/EnemyHealthBar
@onready var inventory_ui: InventoryUI = $UI/InventoryUI

# Component references for easy access
var player_stats: StatsComponent
var player_health: HealthComponent
var player_buffs: BuffComponent
var player_items: ItemComponent
var player_combat: CombatComponent

var enemy_stats: StatsComponent
var enemy_health: HealthComponent
var enemy_buffs: BuffComponent
var enemy_items: ItemComponent
var enemy_combat: CombatComponent

var inventory_visible: bool = false

func _ready() -> void:
	_initialize_components()
	_connect_ui_components()
	_setup_initial_equipment()
	_display_system_info()

func _input(event: InputEvent) -> void:
	if event.is_action_pressed("ui_accept"):  # Space key
		_perform_player_attack()
	elif event.is_action_pressed("heal_player"):  # H key
		_heal_player()
	elif event.is_action_pressed("toggle_inventory"):  # I key
		_toggle_inventory()
	elif event.is_action_pressed("generate_items"):  # R key
		_generate_random_items()
	elif event.is_action_pressed("buff_1"):  # 1 key
		_apply_attack_buff()
	elif event.is_action_pressed("buff_2"):  # 2 key
		_apply_defense_buff()
	elif event.is_action_pressed("buff_3"):  # 3 key
		_apply_speed_buff()
	elif event.is_action_pressed("buff_4"):  # 4 key
		_apply_critical_buff()
	elif event.is_action_pressed("buff_5"):  # 5 key
		_apply_resistance_buff()

## Initializes all component references
func _initialize_components() -> void:
	# Player components
	player_stats = player.get_node("StatsComponent")
	player_health = player.get_node("HealthComponent")
	player_buffs = player.get_node("BuffComponent")
	player_items = player.get_node("ItemComponent")
	player_combat = player.get_node("CombatComponent")
	
	# Enemy components
	enemy_stats = enemy.get_node("StatsComponent")
	enemy_health = enemy.get_node("HealthComponent")
	enemy_buffs = enemy.get_node("BuffComponent")
	enemy_items = enemy.get_node("ItemComponent")
	enemy_combat = enemy.get_node("CombatComponent")

## Connects UI components to their respective data sources
func _connect_ui_components() -> void:
	# Connect health bars
	player_health_bar.connect_to_health_component(player_health)
	enemy_health_bar.connect_to_health_component(enemy_health)
	
	# Connect inventory UI
	inventory_ui.connect_to_item_component(player_items)
	inventory_ui.item_equipped.connect(_on_item_equipped)
	
	# Connect combat events for damage display
	player_combat.attack_performed.connect(_on_attack_performed)
	enemy_combat.attack_performed.connect(_on_attack_performed)
	
	# Connect health events for damage display
	player_health.damage_taken.connect(_on_damage_taken.bind(player))
	enemy_health.damage_taken.connect(_on_damage_taken.bind(enemy))
	player_health.healed.connect(_on_healed.bind(player))
	enemy_health.healed.connect(_on_healed.bind(enemy))

## Sets up initial equipment for testing
func _setup_initial_equipment() -> void:
	# Give player a starting weapon
	var item_factory_script = preload("res://core/factories/ItemFactory.gd")
	var starting_weapon: WeaponResource = item_factory_script.create_weapon_from_template("iron_sword")
	if starting_weapon:
		player_items.add_to_inventory(starting_weapon)
		player_items.equip_weapon(starting_weapon)

	# Give player some armor
	var starting_armor: ArmorResource = item_factory_script.create_armor_from_template("leather_helmet")
	if starting_armor:
		player_items.add_to_inventory(starting_armor)
		player_items.equip_armor(starting_armor)

	# Give enemy basic equipment
	var enemy_weapon: WeaponResource = item_factory_script.create_weapon("Rusty Sword", 0, 8.0)
	if enemy_weapon:
		enemy_items.add_to_inventory(enemy_weapon)
		enemy_items.equip_weapon(enemy_weapon)

## Performs a player attack on the enemy
func _perform_player_attack() -> void:
	if player_combat.attack_target(enemy):
		print("Player attacks enemy!")
	else:
		print("Player cannot attack right now")

## Heals the player
func _heal_player() -> void:
	var heal_amount: float = 25.0
	player_health.heal(heal_amount)
	print("Player healed for ", heal_amount, " HP")

## Toggles inventory visibility
func _toggle_inventory() -> void:
	inventory_visible = not inventory_visible
	inventory_ui.set_visible_state(inventory_visible)

## Generates random items for testing
func _generate_random_items() -> void:
	var item_factory_script = preload("res://core/factories/ItemFactory.gd")

	# Generate random weapons
	for i in range(3):
		var weapon: WeaponResource = item_factory_script.create_random_weapon(1, 5)
		player_items.add_to_inventory(weapon)

	# Generate random armor
	for i in range(2):
		var armor: ArmorResource = item_factory_script.create_random_armor(1, 5)
		player_items.add_to_inventory(armor)

	print("Generated random items!")

## Applies attack buff to player
func _apply_attack_buff() -> void:
	player_buffs.apply_multiplier_buff("Attack Boost", "attack", 1.5, 10.0)
	print("Applied Attack Boost (+50% damage for 10s)")

## Applies defense buff to player
func _apply_defense_buff() -> void:
	player_buffs.apply_multiplier_buff("Defense Boost", "damage_reduction", 0.3, 15.0)
	print("Applied Defense Boost (30% damage reduction for 15s)")

## Applies speed buff to player
func _apply_speed_buff() -> void:
	player_buffs.apply_multiplier_buff("Speed Boost", "attack_speed", 2.0, 8.0)
	print("Applied Speed Boost (2x attack speed for 8s)")

## Applies critical buff to player
func _apply_critical_buff() -> void:
	player_buffs.apply_flat_buff("Critical Boost", "critical_chance", 0.25, 12.0)
	print("Applied Critical Boost (+25% crit chance for 12s)")

## Applies resistance buff to player
func _apply_resistance_buff() -> void:
	player_buffs.apply_flat_buff("Fire Resistance", "resistance_fire", 0.5, 20.0)
	print("Applied Fire Resistance (50% fire resistance for 20s)")

## Handles attack performed events
func _on_attack_performed(_target: Node, damage: float, is_critical: bool) -> void:
	print("Attack dealt ", damage, " damage", " (CRITICAL)" if is_critical else "")

## Handles damage taken events for damage display
func _on_damage_taken(amount: float, damage_type: DamageType.Type, is_critical: bool, target: Node) -> void:
	if damage_display:
		damage_display.show_damage(amount, target.global_position, damage_type, is_critical)

## Handles healing events for damage display
func _on_healed(amount: float, target: Node) -> void:
	if damage_display:
		damage_display.show_healing(amount, target.global_position)

## Handles item equipped events
func _on_item_equipped(item: Resource) -> void:
	if item is WeaponResource:
		player_items.equip_weapon(item as WeaponResource)
		print("Equipped weapon: ", item.item_name)
	elif item is ArmorResource:
		player_items.equip_armor(item as ArmorResource)
		print("Equipped armor: ", item.item_name)

## Displays system information
func _display_system_info() -> void:
	print("=== Damage Calculation System Demo ===")
	print("Player Stats:")
	print("  Level: ", player_stats.level)
	print("  Health: ", player_stats.get_max_health())
	print("  Attack: ", player_stats.get_total_attack())
	print("  Armor: ", player_stats.get_total_armor())
	print("  Crit Chance: ", player_stats.critical_chance * 100, "%")
	print("")
	print("Enemy Stats:")
	print("  Level: ", enemy_stats.level)
	print("  Health: ", enemy_stats.get_max_health())
	print("  Attack: ", enemy_stats.get_total_attack())
	print("  Armor: ", enemy_stats.get_total_armor())
	print("")
	print("Controls:")
	print("  SPACE - Attack Enemy")
	print("  H - Heal Player")
	print("  I - Toggle Inventory")
	print("  R - Generate Random Items")
	print("  1-5 - Apply Various Buffs")
	print("=====================================")

## Called when the scene is about to be freed
func _exit_tree() -> void:
	# Clean up connections
	if player_health_bar:
		player_health_bar.disconnect_from_health_component()
	if enemy_health_bar:
		enemy_health_bar.disconnect_from_health_component()
	if inventory_ui:
		inventory_ui.disconnect_from_item_component()
