# Professional Damage Calculation System for Godot 4.4

A complete, production-ready damage calculation system built with clean architecture principles, SOLID design patterns, and mobile optimization in mind.

## 🏗️ Architecture Overview

This system follows professional software development practices:

- **SOLID Principles**: Single Responsibility, Open/Closed, Dependency Inversion
- **Component-Based Design**: Modular, reusable components
- **Factory Pattern**: Clean item creation and management
- **Resource-Driven**: Data-driven design using Godot Resources
- **Signal-Based Decoupling**: Loose coupling between systems
- **Mobile Optimized**: Minimal allocations, efficient processing

## 📁 Project Structure

```
res://
├── core/
│   ├── enums/
│   │   ├── DamageType.gd          # Damage type enumeration
│   │   └── ItemType.gd            # Item type enumeration
│   ├── interfaces/
│   │   └── IDamageable.gd         # Interface for damageable entities
│   ├── combat/
│   │   └── DamageCalculator.gd    # Core damage calculation logic
│   └── factories/
│       └── ItemFactory.gd        # Factory for creating items
├── data/
│   └── resources/
│       ├── WeaponResource.gd      # Weapon data resource
│       └── ArmorResource.gd       # Armor data resource
├── components/
│   ├── StatsComponent.gd          # Entity statistics management
│   ├── BuffComponent.gd           # Temporary effects system
│   ├── HealthComponent.gd         # Health and damage reception
│   ├── ItemComponent.gd           # Equipment and inventory
│   └── CombatComponent.gd         # Combat actions and targeting
├── ui/
│   ├── DamageDisplay.gd           # Floating damage numbers
│   ├── HealthBar.gd               # Health visualization
│   └── InventoryUI.gd             # Inventory management UI
└── scenes/
    ├── Player.tscn                # Complete player setup
    ├── TestCombat.tscn            # Full demo scene
    └── SimpleTest.tscn            # Basic testing scene
```

## 🎮 Features

### Core Combat System
- **Accurate Damage Calculation**: Hit chance, critical hits, armor reduction
- **Multiple Damage Types**: Physical, Fire, Ice, Lightning, Poison, Holy, Dark
- **Resistance System**: Type-specific damage resistances
- **Critical Hit System**: Configurable chance and multipliers

### Equipment System
- **Weapon Types**: Swords, Bows, Staves, Daggers
- **Armor System**: Helmet, Chest, Legs, Boots, Gloves
- **Durability System**: Equipment degrades with use
- **Requirement System**: Level and attribute requirements

### Buff/Debuff System
- **Temporary Effects**: Timed buffs and debuffs
- **Multiplicative & Additive**: Both types of stat modifications
- **Automatic Cleanup**: Expired effects are automatically removed

### UI Components
- **Floating Damage Numbers**: Animated damage display with type colors
- **Health Bars**: Smooth animated health visualization
- **Inventory System**: Drag-and-drop equipment management
- **Tooltips**: Detailed item information

## 🚀 Quick Start

### Basic Usage

1. **Create an Entity with Components**:
```gdscript
# Add components to your entity
var stats = StatsComponent.new()
var health = HealthComponent.new()
var combat = CombatComponent.new()
entity.add_child(stats)
entity.add_child(health)
entity.add_child(combat)
```

2. **Perform Combat**:
```gdscript
# Calculate and apply damage
var damage_result = DamageCalculator.calculate_damage(attacker, target, DamageType.PHYSICAL)
if damage_result.hit:
    target.take_damage(damage_result.damage, attacker, damage_result.damage_type, damage_result.is_critical)
```

3. **Create Items**:
```gdscript
# Using the factory
var weapon = ItemFactory.create_weapon_from_template("iron_sword")
var armor = ItemFactory.create_random_armor(1, 5)
```

### Testing the System

Run the project to see the simple test in action, or switch to `TestCombat.tscn` for the full demo with UI.

**Controls for Full Demo**:
- `SPACE` - Attack Enemy
- `H` - Heal Player
- `I` - Toggle Inventory
- `R` - Generate Random Items
- `1-5` - Apply Various Buffs

## 🔧 Customization

### Adding New Damage Types
```gdscript
# In DamageType.gd, add to the enum:
enum Type {
    # ... existing types
    ARCANE = 7,
    NATURE = 8
}
```

### Creating Custom Items
```gdscript
# Create weapons programmatically
var custom_weapon = ItemFactory.create_weapon(
    "Flame Sword",
    ItemType.Type.WEAPON_SWORD,
    25.0,  # damage
    DamageType.Type.FIRE,  # damage type
    1.2,   # attack speed
    0.15,  # crit chance
    0.3    # crit damage bonus
)
```

### Adding New Components
Follow the component pattern:
```gdscript
class_name MyComponent
extends Node

# Component-specific functionality
# Use signals for communication
# Keep single responsibility
```

## 📱 Mobile Optimization

The system is optimized for mobile performance:

- **Object Pooling**: Damage display uses pooled labels
- **Cached Calculations**: Stats are cached and invalidated only when needed
- **Minimal Allocations**: Reuses objects where possible
- **Efficient Updates**: Only updates when necessary

## 🧪 Testing

The system includes comprehensive testing:

- **SimpleTest.tscn**: Basic functionality testing
- **TestCombat.tscn**: Full system demonstration
- **Component Tests**: Individual component validation

## 📚 Architecture Principles

### SOLID Principles Applied

1. **Single Responsibility**: Each component has one clear purpose
2. **Open/Closed**: Easy to extend without modifying existing code
3. **Liskov Substitution**: Components can be swapped seamlessly
4. **Interface Segregation**: Clean, focused interfaces
5. **Dependency Inversion**: Depends on abstractions, not concretions

### Design Patterns Used

- **Component Pattern**: Modular, reusable functionality
- **Factory Pattern**: Centralized item creation
- **Observer Pattern**: Signal-based communication
- **Strategy Pattern**: Pluggable damage calculations

## 🎯 Production Ready

This system is designed for production use with:

- **Error Handling**: Graceful degradation and error recovery
- **Performance Monitoring**: Efficient algorithms and caching
- **Scalability**: Easy to extend and modify
- **Maintainability**: Clean, documented code
- **Type Safety**: Fully typed GDScript

## 📄 License

This damage calculation system is provided as a professional example of clean architecture in Godot 4.4. Feel free to use and modify for your projects.
