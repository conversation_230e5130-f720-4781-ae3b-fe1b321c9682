## Component responsible for managing temporary buffs and debuffs
## Follows Single Responsibility Principle - only handles buff management
## Uses signals for decoupling from other systems
class_name BuffComponent
extends Node

signal buff_applied(buff_name: String, duration: float)
signal buff_removed(buff_name: String)
signal buff_expired(buff_name: String)

## Data structure for individual buffs
class Buff:
	var name: String
	var multipliers: Dictionary = {}
	var flat_bonuses: Dictionary = {}
	var duration: float
	var remaining_time: float
	var is_permanent: bool = false
	
	func _init(buff_name: String, buff_duration: float = -1.0) -> void:
		name = buff_name
		duration = buff_duration
		remaining_time = buff_duration
		is_permanent = buff_duration < 0.0

# Active buffs dictionary
var _active_buffs: Dictionary = {}

## Applies a multiplicative buff to a stat
func apply_multiplier_buff(buff_name: String, stat_name: String, multiplier: float, duration: float = -1.0) -> void:
	var buff: Buff = _get_or_create_buff(buff_name, duration)
	buff.multipliers[stat_name] = multiplier
	emit_signal("buff_applied", buff_name, duration)

## Applies a flat bonus buff to a stat
func apply_flat_buff(buff_name: String, stat_name: String, bonus: float, duration: float = -1.0) -> void:
	var buff: Buff = _get_or_create_buff(buff_name, duration)
	buff.flat_bonuses[stat_name] = bonus
	emit_signal("buff_applied", buff_name, duration)

## Removes a specific buff
func remove_buff(buff_name: String) -> void:
	if _active_buffs.has(buff_name):
		_active_buffs.erase(buff_name)
		emit_signal("buff_removed", buff_name)

## Removes all buffs
func clear_all_buffs() -> void:
	var buff_names: Array = _active_buffs.keys()
	_active_buffs.clear()
	for buff_name in buff_names:
		emit_signal("buff_removed", buff_name)

## Returns the total multiplier for a stat from all active buffs
func get_stat_multiplier(stat_name: String) -> float:
	var total_multiplier: float = 1.0
	for buff in _active_buffs.values():
		if buff.multipliers.has(stat_name):
			total_multiplier *= buff.multipliers[stat_name]
	return total_multiplier

## Returns the total flat bonus for a stat from all active buffs
func get_stat_flat_bonus(stat_name: String) -> float:
	var total_bonus: float = 0.0
	for buff in _active_buffs.values():
		if buff.flat_bonuses.has(stat_name):
			total_bonus += buff.flat_bonuses[stat_name]
	return total_bonus

## Returns whether a specific buff is active
func has_buff(buff_name: String) -> bool:
	return _active_buffs.has(buff_name)

## Returns the remaining time for a buff
func get_buff_remaining_time(buff_name: String) -> float:
	if _active_buffs.has(buff_name):
		return _active_buffs[buff_name].remaining_time
	return 0.0

## Returns all active buff names
func get_active_buff_names() -> Array[String]:
	var names: Array[String] = []
	for buff_name in _active_buffs.keys():
		names.append(buff_name)
	return names

## Updates buff timers (called by _process)
func _process(delta: float) -> void:
	var expired_buffs: Array[String] = []
	
	for buff_name in _active_buffs.keys():
		var buff: Buff = _active_buffs[buff_name]
		if not buff.is_permanent:
			buff.remaining_time -= delta
			if buff.remaining_time <= 0.0:
				expired_buffs.append(buff_name)
	
	# Remove expired buffs
	for buff_name in expired_buffs:
		_active_buffs.erase(buff_name)
		emit_signal("buff_expired", buff_name)

## Gets or creates a buff with the given name
func _get_or_create_buff(buff_name: String, duration: float) -> Buff:
	if not _active_buffs.has(buff_name):
		_active_buffs[buff_name] = Buff.new(buff_name, duration)
	return _active_buffs[buff_name]
