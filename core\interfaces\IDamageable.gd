## Interface for entities that can receive damage
## Implements the Dependency Inversion Principle by defining a contract
## that damage dealers can depend on without knowing concrete implementations
class_name IDamageable

## Takes damage from a source with specified type
## @param amount: The amount of damage to take
## @param source: The entity dealing the damage
## @param damage_type: The type of damage being dealt (from DamageType enum)
## @param is_critical: Whether this damage is a critical hit
func take_damage(_amount: float, _source: Node, _damage_type: int, _is_critical: bool = false) -> void:
	push_error("IDamageable.take_damage() must be implemented by subclass")

## Returns whether this entity can currently take damage
## Useful for invincibility frames, death states, etc.
func can_take_damage() -> bool:
	push_error("IDamageable.can_take_damage() must be implemented by subclass")
	return false

## Returns the current health percentage (0.0 to 1.0)
## Used for UI updates and AI decision making
func get_health_percentage() -> float:
	push_error("IDamageable.get_health_percentage() must be implemented by subclass")
	return 0.0
