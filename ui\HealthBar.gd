## UI component for displaying entity health bars
## Follows Single Responsibility Principle - only handles health visualization
## Optimized for mobile with minimal draw calls and efficient updates
class_name HealthBar
extends Control

signal health_bar_clicked

@export var show_text: bool = true
@export var show_background: bool = true
@export var animate_changes: bool = true
@export var animation_duration: float = 0.3

@onready var background_bar: ProgressBar = $BackgroundBar
@onready var health_bar: ProgressBar = $HealthBar
@onready var health_label: Label = $HealthLabel

var target_health_component: HealthComponent
var current_percentage: float = 1.0
var target_percentage: float = 1.0

func _ready() -> void:
	_setup_ui_elements()

## Connects to a health component to display its health
func connect_to_health_component(health_component: HealthComponent) -> void:
	if target_health_component:
		_disconnect_from_current_target()
	
	target_health_component = health_component
	if target_health_component:
		target_health_component.health_changed.connect(_on_health_changed)
		target_health_component.died.connect(_on_target_died)
		target_health_component.revived.connect(_on_target_revived)
		
		# Initialize with current health
		_update_health_display(
			target_health_component.get_current_health(),
			target_health_component.get_max_health(),
			target_health_component.get_health_percentage()
		)

## Disconnects from the current health component
func disconnect_from_health_component() -> void:
	_disconnect_from_current_target()

## Sets health values manually (for non-component entities)
func set_health_values(current: float, maximum: float) -> void:
	var percentage: float = current / maximum if maximum > 0 else 0.0
	_update_health_display(current, maximum, percentage)

## Sets the health bar colors
func set_health_colors(healthy_color: Color, damaged_color: Color, critical_color: Color) -> void:
	# Create a gradient for smooth color transitions
	var gradient: Gradient = Gradient.new()
	gradient.add_point(0.0, critical_color)    # 0% health
	gradient.add_point(0.25, damaged_color)    # 25% health
	gradient.add_point(1.0, healthy_color)     # 100% health
	
	# Apply gradient to health bar
	var style_box: StyleBoxFlat = StyleBoxFlat.new()
	style_box.bg_color = healthy_color
	health_bar.add_theme_stylebox_override("fill", style_box)

## Shows or hides the health bar
func set_visible_state(should_be_visible: bool) -> void:
	visible = should_be_visible

## Sets up the UI elements
func _setup_ui_elements() -> void:
	# Setup background bar
	if background_bar:
		background_bar.value = 100
		background_bar.modulate = Color(0.3, 0.3, 0.3, 0.8)
		background_bar.visible = show_background
	
	# Setup health bar
	if health_bar:
		health_bar.value = 100
		health_bar.modulate = Color.WHITE
	
	# Setup health label
	if health_label:
		health_label.visible = show_text
		health_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		health_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER

## Handles health changed signal
func _on_health_changed(current_health: float, max_health: float, percentage: float) -> void:
	_update_health_display(current_health, max_health, percentage)

## Handles target death
func _on_target_died() -> void:
	_update_health_color_for_percentage(0.0)
	if health_label and show_text:
		health_label.text = "DEAD"
		health_label.modulate = Color.RED

## Handles target revival
func _on_target_revived() -> void:
	if health_label and show_text:
		health_label.modulate = Color.WHITE

## Updates the health display
func _update_health_display(current: float, maximum: float, percentage: float) -> void:
	target_percentage = percentage
	
	if animate_changes:
		_animate_to_target_percentage()
	else:
		_set_immediate_percentage(percentage)
	
	_update_health_text(current, maximum)
	_update_health_color_for_percentage(percentage)

## Animates the health bar to the target percentage
func _animate_to_target_percentage() -> void:
	if not health_bar:
		return
	
	var tween: Tween = create_tween()
	tween.tween_method(
		_set_health_bar_value,
		current_percentage * 100.0,
		target_percentage * 100.0,
		animation_duration
	)
	tween.tween_callback(_on_animation_complete)

## Sets the health bar value immediately
func _set_immediate_percentage(percentage: float) -> void:
	current_percentage = percentage
	if health_bar:
		health_bar.value = percentage * 100.0

## Sets the health bar value (used by tween)
func _set_health_bar_value(value: float) -> void:
	if health_bar:
		health_bar.value = value
	current_percentage = value / 100.0

## Called when animation completes
func _on_animation_complete() -> void:
	current_percentage = target_percentage

## Updates the health text display
func _update_health_text(current: float, maximum: float) -> void:
	if not health_label or not show_text:
		return
	
	health_label.text = "%d / %d" % [int(current), int(maximum)]

## Updates health bar color based on percentage
func _update_health_color_for_percentage(percentage: float) -> void:
	if not health_bar:
		return
	
	var color: Color
	if percentage > 0.6:
		color = Color.GREEN
	elif percentage > 0.3:
		color = Color.YELLOW
	else:
		color = Color.RED
	
	health_bar.modulate = color

## Disconnects from current target
func _disconnect_from_current_target() -> void:
	if target_health_component:
		if target_health_component.health_changed.is_connected(_on_health_changed):
			target_health_component.health_changed.disconnect(_on_health_changed)
		if target_health_component.died.is_connected(_on_target_died):
			target_health_component.died.disconnect(_on_target_died)
		if target_health_component.revived.is_connected(_on_target_revived):
			target_health_component.revived.disconnect(_on_target_revived)
		target_health_component = null

## Handles mouse input for clicking
func _gui_input(event: InputEvent) -> void:
	if event is InputEventMouseButton:
		var mouse_event: InputEventMouseButton = event as InputEventMouseButton
		if mouse_event.pressed and mouse_event.button_index == MOUSE_BUTTON_LEFT:
			emit_signal("health_bar_clicked")
