## Component responsible for handling combat actions and damage dealing
## Follows Single Responsibility Principle - only handles combat mechanics
## Uses dependency injection for damage calculation and target validation
class_name CombatComponent
extends Node

signal attack_performed(target: Node, damage: float, is_critical: bool)
signal attack_missed(target: Node)
signal combat_state_changed(is_in_combat: bool)

@export var attack_range: float = 50.0
@export var attack_cooldown: float = 1.0
@export var can_attack_while_moving: bool = true

var is_in_combat: bool = false
var last_attack_time: float = 0.0
var current_target: Node = null

# Component dependencies
var _stats_component: StatsComponent
var _buff_component: BuffComponent
var _item_component: ItemComponent

func _ready() -> void:
	_initialize_components()

func _process(delta: float) -> void:
	_update_combat_state()

## Attempts to attack a target if conditions are met
func attack_target(target: Node) -> bool:
	if not _can_attack(target):
		return false
	
	var damage_result: Dictionary = DamageCalculator.calculate_damage(
		get_parent(),
		target,
		_get_weapon_damage_type()
	)
	
	if damage_result.hit:
		_perform_successful_attack(target, damage_result)
	else:
		_perform_missed_attack(target)
	
	_set_attack_cooldown()
	return true

## Sets the current combat target
func set_target(target: Node) -> void:
	if current_target != target:
		current_target = target
		_update_combat_state()

## Clears the current combat target
func clear_target() -> void:
	if current_target:
		current_target = null
		_update_combat_state()

## Returns whether the entity can currently attack
func can_attack() -> bool:
	return _is_attack_ready() and not _is_on_cooldown()

## Returns the current weapon's damage type
func get_weapon_damage_type() -> DamageType.Type:
	return _get_weapon_damage_type()

## Returns the effective attack range including bonuses
func get_effective_attack_range() -> float:
	var base_range: float = attack_range
	
	# Weapon might modify range
	if _item_component and _item_component.equipped_weapon:
		# Some weapons might have range modifiers
		pass
	
	# Buffs might modify range
	if _buff_component:
		base_range *= _buff_component.get_stat_multiplier("attack_range")
	
	return base_range

## Returns the effective attack speed including bonuses
func get_effective_attack_speed() -> float:
	var base_speed: float = 1.0 / attack_cooldown
	
	if _stats_component:
		base_speed *= _stats_component.attack_speed
	
	if _buff_component:
		base_speed *= _buff_component.get_stat_multiplier("attack_speed")
	
	return base_speed

## Forces the entity into combat state
func enter_combat() -> void:
	if not is_in_combat:
		is_in_combat = true
		emit_signal("combat_state_changed", true)

## Forces the entity out of combat state
func exit_combat() -> void:
	if is_in_combat:
		is_in_combat = false
		current_target = null
		emit_signal("combat_state_changed", false)

## Initializes component dependencies
func _initialize_components() -> void:
	_stats_component = get_parent().get_node_or_null("StatsComponent") as StatsComponent
	_buff_component = get_parent().get_node_or_null("BuffComponent") as BuffComponent
	_item_component = get_parent().get_node_or_null("ItemComponent") as ItemComponent
	
	if not _stats_component:
		push_warning("CombatComponent: No StatsComponent found on parent")

## Checks if the entity can attack the target
func _can_attack(target: Node) -> bool:
	if not target or not _is_attack_ready():
		return false
	
	# Check if target is damageable
	if not (target.has_method("take_damage") or target.get_script() and target.get_script().has_method("take_damage")):
		return false
	
	# Check range
	var parent_node: Node3D = get_parent() as Node3D
	var target_node: Node3D = target as Node3D
	if parent_node and target_node:
		var distance: float = parent_node.global_position.distance_to(target_node.global_position)
		if distance > get_effective_attack_range():
			return false
	
	# Check line of sight (basic implementation)
	return _has_line_of_sight(target)

## Checks if attack is ready (not on cooldown)
func _is_attack_ready() -> bool:
	return not _is_on_cooldown()

## Checks if attack is on cooldown
func _is_on_cooldown() -> bool:
	var current_time: float = Time.get_time_dict_from_system()["second"] + Time.get_time_dict_from_system()["minute"] * 60.0
	var effective_cooldown: float = attack_cooldown / get_effective_attack_speed()
	return (current_time - last_attack_time) < effective_cooldown

## Sets the attack cooldown timer
func _set_attack_cooldown() -> void:
	last_attack_time = Time.get_time_dict_from_system()["second"] + Time.get_time_dict_from_system()["minute"] * 60.0

## Performs a successful attack on the target
func _perform_successful_attack(target: Node, damage_result: Dictionary) -> void:
	var damage: float = damage_result.damage
	var is_critical: bool = damage_result.is_critical
	var damage_type: int = damage_result.damage_type
	
	# Apply damage to target
	if target.has_method("take_damage"):
		target.take_damage(damage, get_parent(), damage_type, is_critical)
	
	# Reduce weapon durability
	if _item_component and _item_component.equipped_weapon:
		_item_component.equipped_weapon.use_weapon()
	
	emit_signal("attack_performed", target, damage, is_critical)
	enter_combat()

## Performs a missed attack
func _perform_missed_attack(target: Node) -> void:
	emit_signal("attack_missed", target)

## Returns the damage type of the equipped weapon
func _get_weapon_damage_type() -> int:
	if _item_component and _item_component.equipped_weapon:
		return _item_component.equipped_weapon.damage_type
	return 0  # DamageType.Type.PHYSICAL

## Basic line of sight check (can be enhanced with raycasting)
func _has_line_of_sight(_target: Node) -> bool:
	# Simple implementation - always return true
	# In a real game, you'd use raycasting to check for obstacles
	return true

## Updates combat state based on current conditions
func _update_combat_state() -> void:
	var should_be_in_combat: bool = current_target != null
	
	if should_be_in_combat and not is_in_combat:
		enter_combat()
	elif not should_be_in_combat and is_in_combat:
		exit_combat()
