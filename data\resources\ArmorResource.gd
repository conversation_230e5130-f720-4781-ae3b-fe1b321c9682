## Resource class for armor data
## Follows the Resource pattern for data-driven design
## Allows armor to be created and modified in the Godot editor
class_name ArmorResource
extends Resource

@export_group("Basic Properties")
@export var item_name: String = "Unknown Armor"
@export var item_type: ItemType.Type = ItemType.Type.ARMOR_CHEST
@export var description: String = ""
@export var icon: Texture2D

@export_group("Defense Stats")
@export var armor_value: float = 5.0
@export var resistances: Dictionary = {
	0: 0.0,  # PHYSICAL
	1: 0.0,  # FIRE
	2: 0.0,  # ICE
	3: 0.0,  # LIGHTNING
	4: 0.0,  # POISON
	5: 0.0,  # HOLY
	6: 0.0   # DARK
}

@export_group("Stat Bonuses")
@export var health_bonus: float = 0.0
@export var mana_bonus: float = 0.0
@export var strength_bonus: int = 0
@export var dexterity_bonus: int = 0
@export var intelligence_bonus: int = 0

@export_group("Requirements")
@export var required_level: int = 1
@export var required_strength: int = 0
@export var required_dexterity: int = 0
@export var required_intelligence: int = 0

@export_group("Special Properties")
@export var special_effects: Array[String] = []
@export var durability: int = 100
@export var max_durability: int = 100

## Returns the effective armor value considering durability
func get_effective_armor() -> float:
	var durability_factor: float = float(durability) / float(max_durability)
	return armor_value * durability_factor

## Returns the resistance value for a specific damage type
func get_resistance(damage_type: int) -> float:
	var base_resistance: float = resistances.get(damage_type, 0.0)
	var durability_factor: float = float(durability) / float(max_durability)
	return base_resistance * durability_factor

## Returns whether this armor can be equipped by an entity with given stats
func can_equip(level: int, strength: int, dexterity: int, intelligence: int) -> bool:
	return (level >= required_level and 
			strength >= required_strength and 
			dexterity >= required_dexterity and 
			intelligence >= required_intelligence)

## Reduces durability when armor absorbs damage
func take_durability_damage(damage_amount: float) -> void:
	var durability_loss: int = max(1, int(damage_amount / 10.0))
	durability = max(0, durability - durability_loss)

## Repairs the armor to full durability
func repair() -> void:
	durability = max_durability

## Returns the armor's condition as a percentage
func get_condition_percentage() -> float:
	return float(durability) / float(max_durability)
