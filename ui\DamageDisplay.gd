## UI component for displaying floating damage numbers
## Follows Single Responsibility Principle - only handles damage visualization
## Uses object pooling for mobile performance optimization
class_name DamageDisplay
extends Control

@export var damage_label_scene: PackedScene
@export var max_pool_size: int = 20
@export var animation_duration: float = 1.5
@export var float_distance: float = 100.0

# Object pool for damage labels
var _label_pool: Array[Label] = []
var _active_labels: Array[Label] = []

func _ready() -> void:
	_initialize_pool()

## Shows damage at the specified world position
func show_damage(
	amount: float,
	world_position: Vector3,
	damage_type: DamageType.Type,
	is_critical: bool = false
) -> void:
	var label: Label = _get_pooled_label()
	if not label:
		return
	
	_setup_damage_label(label, amount, damage_type, is_critical)
	_position_label(label, world_position)
	_animate_damage_label(label)

## Shows healing at the specified world position
func show_healing(amount: float, world_position: Vector3) -> void:
	var label: Label = _get_pooled_label()
	if not label:
		return
	
	_setup_healing_label(label, amount)
	_position_label(label, world_position)
	_animate_damage_label(label)

## Initializes the object pool
func _initialize_pool() -> void:
	for i in range(max_pool_size):
		var label: Label = _create_damage_label()
		label.visible = false
		_label_pool.append(label)

## Creates a new damage label
func _create_damage_label() -> Label:
	var label: Label = Label.new()
	add_child(label)
	
	# Set default properties
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	label.add_theme_font_size_override("font_size", 24)
	label.add_theme_color_override("font_shadow_color", Color.BLACK)
	label.add_theme_constant_override("shadow_offset_x", 2)
	label.add_theme_constant_override("shadow_offset_y", 2)
	
	return label

## Gets a label from the pool or creates a new one
func _get_pooled_label() -> Label:
	if _label_pool.size() > 0:
		var label: Label = _label_pool.pop_back()
		_active_labels.append(label)
		return label
	
	# Pool exhausted, try to reuse oldest active label
	if _active_labels.size() > 0:
		var label: Label = _active_labels[0]
		_return_label_to_pool(label)
		_active_labels.append(label)
		return label
	
	return null

## Returns a label to the pool
func _return_label_to_pool(label: Label) -> void:
	if label in _active_labels:
		_active_labels.erase(label)
	
	label.visible = false
	label.modulate = Color.WHITE
	label.scale = Vector2.ONE
	
	if _label_pool.size() < max_pool_size:
		_label_pool.append(label)

## Sets up a damage label with appropriate styling
func _setup_damage_label(label: Label, amount: float, damage_type: DamageType.Type, is_critical: bool) -> void:
	var damage_text: String = str(int(amount))
	
	if is_critical:
		damage_text = "CRIT! " + damage_text
		label.add_theme_font_size_override("font_size", 32)
		label.scale = Vector2(1.2, 1.2)
	else:
		label.add_theme_font_size_override("font_size", 24)
		label.scale = Vector2.ONE
	
	label.text = damage_text
	label.add_theme_color_override("font_color", DamageType.get_type_color(damage_type))
	label.visible = true

## Sets up a healing label with appropriate styling
func _setup_healing_label(label: Label, amount: float) -> void:
	label.text = "+" + str(int(amount))
	label.add_theme_color_override("font_color", Color.GREEN)
	label.add_theme_font_size_override("font_size", 24)
	label.scale = Vector2.ONE
	label.visible = true

## Positions the label at the world position
func _position_label(label: Label, world_position: Vector3) -> void:
	# Convert world position to screen position
	var camera: Camera3D = get_viewport().get_camera_3d()
	if not camera:
		return
	
	var screen_position: Vector2 = camera.unproject_position(world_position)
	
	# Add some random offset to prevent overlapping
	var random_offset: Vector2 = Vector2(
		randf_range(-30.0, 30.0),
		randf_range(-20.0, 20.0)
	)
	
	label.position = screen_position + random_offset

## Animates the damage label
func _animate_damage_label(label: Label) -> void:
	var tween: Tween = create_tween()
	tween.set_parallel(true)
	
	# Float upward
	var start_pos: Vector2 = label.position
	var end_pos: Vector2 = start_pos + Vector2(0, -float_distance)
	tween.tween_property(label, "position", end_pos, animation_duration)
	
	# Fade out
	tween.tween_property(label, "modulate:a", 0.0, animation_duration)
	
	# Scale animation for critical hits
	if label.scale.x > 1.0:
		tween.tween_property(label, "scale", Vector2.ONE, animation_duration * 0.3)
	
	# Return to pool when animation completes
	tween.tween_callback(_return_label_to_pool.bind(label)).set_delay(animation_duration)
