## Factory class for creating items following the Factory Pattern
## Follows Single Responsibility Principle - only handles item creation
## Provides clean interface for creating different types of items
extends Node

## Predefined weapon templates for easy creation
static var WEAPON_TEMPLATES: Dictionary = {
	"iron_sword": {
		"name": "Iron Sword",
		"type": 0,  # WEAPON_SWORD
		"damage": 15.0,
		"damage_type": 0,  # PHYSICAL
		"attack_speed": 1.0,
		"crit_chance": 0.05,
		"crit_damage": 0.0,
		"required_level": 1,
		"required_strength": 10
	},
	"steel_sword": {
		"name": "Steel Sword",
		"type": 0,  # WEAPON_SWORD
		"damage": 25.0,
		"damage_type": 0,  # PHYSICAL
		"attack_speed": 1.0,
		"crit_chance": 0.08,
		"crit_damage": 0.1,
		"required_level": 5,
		"required_strength": 15
	},
	"fire_staff": {
		"name": "Fire Staff",
		"type": 2,  # WEAPON_STAFF
		"damage": 20.0,
		"damage_type": 1,  # FIRE
		"attack_speed": 0.8,
		"crit_chance": 0.1,
		"crit_damage": 0.2,
		"required_level": 3,
		"required_intelligence": 12
	},
	"hunting_bow": {
		"name": "Hunting Bow",
		"type": 1,  # WEAPON_BOW
		"damage": 18.0,
		"damage_type": 0,  # PHYSICAL
		"attack_speed": 1.2,
		"crit_chance": 0.15,
		"crit_damage": 0.3,
		"required_level": 2,
		"required_dexterity": 12
	}
}

## Predefined armor templates for easy creation
static var ARMOR_TEMPLATES: Dictionary = {
	"leather_helmet": {
		"name": "Leather Helmet",
		"type": 10,  # ARMOR_HELMET
		"armor": 3.0,
		"health_bonus": 5.0,
		"required_level": 1
	},
	"iron_chestplate": {
		"name": "Iron Chestplate",
		"type": 11,  # ARMOR_CHEST
		"armor": 12.0,
		"health_bonus": 15.0,
		"resistances": {
			0: 0.05  # PHYSICAL
		},
		"required_level": 3,
		"required_strength": 12
	},
	"mage_robes": {
		"name": "Mage Robes",
		"type": 11,  # ARMOR_CHEST
		"armor": 5.0,
		"mana_bonus": 25.0,
		"intelligence_bonus": 2,
		"resistances": {
			1: 0.1,  # FIRE
			2: 0.1   # ICE
		},
		"required_level": 2,
		"required_intelligence": 10
	}
}

## Creates a weapon from a template
static func create_weapon_from_template(template_name: String) -> WeaponResource:
	if not WEAPON_TEMPLATES.has(template_name):
		push_error("ItemFactory: Unknown weapon template: " + template_name)
		return null
	
	var template: Dictionary = WEAPON_TEMPLATES[template_name]
	return _create_weapon_from_data(template)

## Creates a custom weapon with specified parameters
static func create_weapon(
	item_name: String,
	weapon_type: ItemType.Type,
	base_damage: float,
	damage_type: DamageType.Type = DamageType.Type.PHYSICAL,
	attack_speed: float = 1.0,
	crit_chance: float = 0.0,
	crit_damage: float = 0.0
) -> WeaponResource:
	var weapon: WeaponResource = WeaponResource.new()

	weapon.item_name = item_name
	weapon.item_type = weapon_type
	weapon.base_damage = base_damage
	weapon.damage_type = damage_type
	weapon.attack_speed = attack_speed
	weapon.critical_chance_bonus = crit_chance
	weapon.critical_damage_bonus = crit_damage
	
	return weapon

## Creates armor from a template
static func create_armor_from_template(template_name: String) -> ArmorResource:
	if not ARMOR_TEMPLATES.has(template_name):
		push_error("ItemFactory: Unknown armor template: " + template_name)
		return null
	
	var template: Dictionary = ARMOR_TEMPLATES[template_name]
	return _create_armor_from_data(template)

## Creates custom armor with specified parameters
static func create_armor(
	item_name: String,
	armor_type: ItemType.Type,
	armor_value: float,
	resistances: Dictionary = {},
	health_bonus: float = 0.0,
	mana_bonus: float = 0.0
) -> ArmorResource:
	var armor: ArmorResource = ArmorResource.new()

	armor.item_name = item_name
	armor.item_type = armor_type
	armor.armor_value = armor_value
	armor.health_bonus = health_bonus
	armor.mana_bonus = mana_bonus
	
	# Set resistances
	for damage_type in resistances.keys():
		armor.resistances[damage_type] = resistances[damage_type]
	
	return armor

## Creates a random weapon within specified level range
static func create_random_weapon(min_level: int = 1, max_level: int = 10) -> WeaponResource:
	var weapon_types: Array = [
		ItemType.Type.WEAPON_SWORD,
		ItemType.Type.WEAPON_BOW,
		ItemType.Type.WEAPON_STAFF,
		ItemType.Type.WEAPON_DAGGER
	]
	
	var damage_types: Array[DamageType.Type] = [
		DamageType.Type.PHYSICAL,
		DamageType.Type.FIRE,
		DamageType.Type.ICE,
		DamageType.Type.LIGHTNING
	]

	var weapon: WeaponResource = WeaponResource.new()
	var level: int = randi_range(min_level, max_level)

	weapon.item_name = _generate_random_weapon_name()
	weapon.item_type = weapon_types[randi() % weapon_types.size()]
	weapon.base_damage = 8.0 + (level * 3.0) + randf_range(-2.0, 2.0)
	weapon.damage_type = damage_types[randi() % damage_types.size()]
	weapon.attack_speed = randf_range(0.7, 1.3)
	weapon.critical_chance_bonus = randf_range(0.0, 0.1)
	weapon.critical_damage_bonus = randf_range(0.0, 0.2)
	weapon.required_level = level
	
	return weapon

## Creates a random armor piece within specified level range
static func create_random_armor(min_level: int = 1, max_level: int = 10) -> ArmorResource:
	var armor_types: Array = [
		ItemType.Type.ARMOR_HELMET,
		ItemType.Type.ARMOR_CHEST,
		ItemType.Type.ARMOR_LEGS,
		ItemType.Type.ARMOR_BOOTS,
		ItemType.Type.ARMOR_GLOVES
	]
	
	var armor: ArmorResource = ArmorResource.new()
	var level: int = randi_range(min_level, max_level)
	
	armor.item_name = _generate_random_armor_name()
	armor.item_type = armor_types[randi() % armor_types.size()]
	armor.armor_value = 3.0 + (level * 2.0) + randf_range(-1.0, 1.0)
	armor.health_bonus = level * 3.0
	armor.required_level = level
	
	# Random resistance
	if randf() < 0.3:  # 30% chance for resistance
		var damage_types: Array[DamageType.Type] = [
			DamageType.Type.FIRE,
			DamageType.Type.ICE,
			DamageType.Type.LIGHTNING,
			DamageType.Type.POISON
		]
		var random_type: DamageType.Type = damage_types[randi() % damage_types.size()]
		armor.resistances[random_type] = randf_range(0.05, 0.15)
	
	return armor

## Returns all available weapon template names
static func get_weapon_template_names() -> Array[String]:
	var names: Array[String] = []
	for template_name in WEAPON_TEMPLATES.keys():
		names.append(template_name)
	return names

## Returns all available armor template names
static func get_armor_template_names() -> Array[String]:
	var names: Array[String] = []
	for template_name in ARMOR_TEMPLATES.keys():
		names.append(template_name)
	return names

## Creates weapon from template data
static func _create_weapon_from_data(data: Dictionary) -> WeaponResource:
	var weapon: WeaponResource = WeaponResource.new()
	
	weapon.item_name = data.get("name", "Unknown Weapon")
	weapon.item_type = data.get("type", ItemType.Type.WEAPON_SWORD)
	weapon.base_damage = data.get("damage", 10.0)
	weapon.damage_type = data.get("damage_type", DamageType.Type.PHYSICAL)
	weapon.attack_speed = data.get("attack_speed", 1.0)
	weapon.critical_chance_bonus = data.get("crit_chance", 0.0)
	weapon.critical_damage_bonus = data.get("crit_damage", 0.0)
	weapon.required_level = data.get("required_level", 1)
	weapon.required_strength = data.get("required_strength", 0)
	weapon.required_dexterity = data.get("required_dexterity", 0)
	weapon.required_intelligence = data.get("required_intelligence", 0)
	
	return weapon

## Creates armor from template data
static func _create_armor_from_data(data: Dictionary) -> ArmorResource:
	var armor: ArmorResource = ArmorResource.new()
	
	armor.item_name = data.get("name", "Unknown Armor")
	armor.item_type = data.get("type", ItemType.Type.ARMOR_CHEST)
	armor.armor_value = data.get("armor", 5.0)
	armor.health_bonus = data.get("health_bonus", 0.0)
	armor.mana_bonus = data.get("mana_bonus", 0.0)
	armor.strength_bonus = data.get("strength_bonus", 0)
	armor.dexterity_bonus = data.get("dexterity_bonus", 0)
	armor.intelligence_bonus = data.get("intelligence_bonus", 0)
	armor.required_level = data.get("required_level", 1)
	armor.required_strength = data.get("required_strength", 0)
	armor.required_dexterity = data.get("required_dexterity", 0)
	armor.required_intelligence = data.get("required_intelligence", 0)
	
	# Set resistances if provided
	if data.has("resistances"):
		for damage_type in data.resistances.keys():
			armor.resistances[damage_type] = data.resistances[damage_type]
	
	return armor

## Generates a random weapon name
static func _generate_random_weapon_name() -> String:
	var prefixes: Array[String] = ["Sharp", "Mighty", "Ancient", "Blessed", "Cursed", "Glowing"]
	var weapon_names: Array[String] = ["Blade", "Sword", "Axe", "Bow", "Staff", "Dagger"]
	var suffixes: Array[String] = ["of Power", "of Speed", "of Fire", "of Ice", "of Lightning"]
	
	var prefix: String = prefixes[randi() % prefixes.size()]
	var weapon: String = weapon_names[randi() % weapon_names.size()]
	var suffix: String = suffixes[randi() % suffixes.size()] if randf() < 0.5 else ""
	
	return prefix + " " + weapon + (" " + suffix if not suffix.is_empty() else "")

## Generates a random armor name
static func _generate_random_armor_name() -> String:
	var materials: Array[String] = ["Leather", "Iron", "Steel", "Mithril", "Dragon"]
	var armor_types: Array[String] = ["Helmet", "Chestplate", "Leggings", "Boots", "Gloves"]
	
	var material: String = materials[randi() % materials.size()]
	var armor_type: String = armor_types[randi() % armor_types.size()]
	
	return material + " " + armor_type
