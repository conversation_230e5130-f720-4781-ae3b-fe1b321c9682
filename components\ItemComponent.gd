## Component responsible for managing equipped items and inventory
## Follows Single Responsibility Principle - only handles item management
## Provides clean interface for equipment and stat bonuses
class_name ItemComponent
extends Node

signal item_equipped(item_resource: Resource, slot: String)
signal item_unequipped(item_resource: Resource, slot: String)
signal inventory_changed

@export var inventory_size: int = 20

# Equipment slots
var equipped_weapon: WeaponResource
var equipped_armor: Dictionary = {
	"helmet": null,
	"chest": null,
	"legs": null,
	"boots": null,
	"gloves": null
}
var equipped_accessories: Dictionary = {
	"ring1": null,
	"ring2": null,
	"amulet": null
}

# Inventory storage
var inventory: Array[Resource] = []

# Component dependencies
var _stats_component: StatsComponent

func _ready() -> void:
	# Defer initialization to ensure all components are added
	call_deferred("_initialize_components")
	call_deferred("_initialize_inventory")

## Equips a weapon if requirements are met
func equip_weapon(weapon: WeaponResource) -> bool:
	if not _can_equip_item(weapon):
		return false
	
	var old_weapon: WeaponResource = equipped_weapon
	equipped_weapon = weapon
	
	if old_weapon:
		_add_to_inventory(old_weapon)
	
	_remove_from_inventory(weapon)
	emit_signal("item_equipped", weapon, "weapon")
	return true

## Equips armor in the appropriate slot
func equip_armor(armor: ArmorResource) -> bool:
	if not _can_equip_item(armor):
		return false
	
	var slot: String = _get_armor_slot(armor.item_type)
	if slot.is_empty():
		return false
	
	var old_armor: ArmorResource = equipped_armor[slot]
	equipped_armor[slot] = armor
	
	if old_armor:
		_add_to_inventory(old_armor)
	
	_remove_from_inventory(armor)
	emit_signal("item_equipped", armor, slot)
	return true

## Unequips weapon and returns it to inventory
func unequip_weapon() -> bool:
	if not equipped_weapon or not _has_inventory_space():
		return false
	
	var weapon: WeaponResource = equipped_weapon
	equipped_weapon = null
	_add_to_inventory(weapon)
	emit_signal("item_unequipped", weapon, "weapon")
	return true

## Unequips armor from specified slot
func unequip_armor(slot: String) -> bool:
	if not equipped_armor.has(slot) or not equipped_armor[slot] or not _has_inventory_space():
		return false
	
	var armor: ArmorResource = equipped_armor[slot]
	equipped_armor[slot] = null
	_add_to_inventory(armor)
	emit_signal("item_unequipped", armor, slot)
	return true

## Adds an item to inventory if there's space
func add_to_inventory(item: Resource) -> bool:
	return _add_to_inventory(item)

## Removes an item from inventory
func remove_from_inventory(item: Resource) -> bool:
	return _remove_from_inventory(item)

## Returns total attack bonus from equipped items
func get_total_attack_bonus() -> float:
	var bonus: float = 0.0
	
	if equipped_weapon:
		bonus += equipped_weapon.get_effective_damage()
	
	# Add bonuses from accessories that might boost attack
	for accessory in equipped_accessories.values():
		if accessory and accessory.has_method("get_attack_bonus"):
			bonus += accessory.get_attack_bonus()
	
	return bonus

## Returns total armor bonus from equipped items
func get_total_armor_bonus() -> float:
	var bonus: float = 0.0
	
	for armor in equipped_armor.values():
		if armor:
			bonus += armor.get_effective_armor()
	
	return bonus

## Returns total resistance for a damage type from equipped items
func get_total_resistance(damage_type: DamageType.Type) -> float:
	var total_resistance: float = 0.0
	
	for armor in equipped_armor.values():
		if armor:
			total_resistance += armor.get_resistance(damage_type)
	
	# Cap total resistance at 95%
	return min(total_resistance, 0.95)

## Returns critical chance bonus from equipped items
func get_critical_chance_bonus() -> float:
	var bonus: float = 0.0
	
	if equipped_weapon:
		bonus += equipped_weapon.critical_chance_bonus
	
	return bonus

## Returns critical damage bonus from equipped items
func get_critical_damage_bonus() -> float:
	var bonus: float = 0.0
	
	if equipped_weapon:
		bonus += equipped_weapon.critical_damage_bonus
	
	return bonus

## Returns the current inventory as an array
func get_inventory() -> Array[Resource]:
	return inventory.duplicate()

## Returns whether inventory has space for more items
func has_inventory_space() -> bool:
	return _has_inventory_space()

## Initializes component dependencies
func _initialize_components() -> void:
	_stats_component = get_parent().get_node_or_null("StatsComponent") as StatsComponent
	
	if not _stats_component:
		push_warning("ItemComponent: No StatsComponent found on parent")

## Initializes empty inventory
func _initialize_inventory() -> void:
	inventory.resize(inventory_size)
	for i in range(inventory_size):
		inventory[i] = null

## Checks if an item can be equipped based on requirements
func _can_equip_item(item: Resource) -> bool:
	if not _stats_component:
		return false
	
	if item is WeaponResource:
		var weapon: WeaponResource = item as WeaponResource
		return weapon.can_equip(
			_stats_component.level,
			_stats_component.strength,
			_stats_component.dexterity,
			_stats_component.intelligence
		)
	elif item is ArmorResource:
		var armor: ArmorResource = item as ArmorResource
		return armor.can_equip(
			_stats_component.level,
			_stats_component.strength,
			_stats_component.dexterity,
			_stats_component.intelligence
		)
	
	return false

## Returns the armor slot for an armor type
func _get_armor_slot(armor_type: int) -> String:
	match armor_type:
		10:  # ARMOR_HELMET
			return "helmet"
		11:  # ARMOR_CHEST
			return "chest"
		12:  # ARMOR_LEGS
			return "legs"
		13:  # ARMOR_BOOTS
			return "boots"
		14:  # ARMOR_GLOVES
			return "gloves"
		_:
			return ""

## Adds item to first available inventory slot
func _add_to_inventory(item: Resource) -> bool:
	if not _has_inventory_space():
		return false
	
	for i in range(inventory.size()):
		if inventory[i] == null:
			inventory[i] = item
			emit_signal("inventory_changed")
			return true
	
	return false

## Removes item from inventory
func _remove_from_inventory(item: Resource) -> bool:
	var index: int = inventory.find(item)
	if index >= 0:
		inventory[index] = null
		emit_signal("inventory_changed")
		return true
	return false

## Checks if inventory has available space
func _has_inventory_space() -> bool:
	return inventory.has(null)
