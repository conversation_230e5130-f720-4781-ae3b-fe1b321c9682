## Simple test script to demonstrate the damage calculation system
## This script creates entities and tests combat without complex UI
extends Node3D

func _ready() -> void:
	print("=== Damage Calculation System Test ===")
	_test_basic_combat()
	_test_item_system()
	_test_buff_system()
	print("=== Test Complete ===")

## Tests basic combat between two entities
func _test_basic_combat() -> void:
	print("\n--- Testing Basic Combat ---")
	
	# Create attacker
	var attacker: Node3D = Node3D.new()
	add_child(attacker)
	
	var attacker_stats: StatsComponent = StatsComponent.new()
	attacker_stats.base_attack = 20.0
	attacker_stats.critical_chance = 0.1
	attacker.add_child(attacker_stats)
	
	var attacker_buffs: BuffComponent = BuffComponent.new()
	attacker.add_child(attacker_buffs)
	
	var attacker_items: ItemComponent = ItemComponent.new()
	attacker.add_child(attacker_items)
	
	# Create target
	var target: Node3D = Node3D.new()
	add_child(target)
	
	var target_stats: StatsComponent = StatsComponent.new()
	target_stats.base_health = 100.0
	target_stats.base_armor = 10.0
	target.add_child(target_stats)
	
	var target_health: HealthComponent = HealthComponent.new()
	target.add_child(target_health)
	
	var target_buffs: BuffComponent = BuffComponent.new()
	target.add_child(target_buffs)
	
	var target_items: ItemComponent = ItemComponent.new()
	target.add_child(target_items)
	
	# Wait a couple frames for components to initialize properly
	await get_tree().process_frame
	await get_tree().process_frame

	print("Attacker Attack: ", attacker_stats.get_total_attack())
	print("Target Health: ", target_health.get_current_health())
	print("Target Armor: ", target_stats.get_total_armor())
	
	# Perform damage calculation
	var damage_calculator_script = preload("res://core/combat/DamageCalculator.gd")
	var damage_result = damage_calculator_script.calculate_damage(attacker, target, 0)  # Physical damage
	
	print("Damage calculated: ", damage_result.damage)
	print("Critical hit: ", damage_result.is_critical)
	print("Hit successful: ", damage_result.hit)
	
	# Apply damage
	if damage_result.hit:
		target_health.take_damage(damage_result.damage, attacker, damage_result.damage_type, damage_result.is_critical)
		print("Target health after damage: ", target_health.get_current_health())
	
	# Clean up
	attacker.queue_free()
	target.queue_free()

## Tests the item system
func _test_item_system() -> void:
	print("\n--- Testing Item System ---")
	
	# Create a weapon
	var weapon: WeaponResource = WeaponResource.new()
	weapon.item_name = "Test Sword"
	weapon.item_type = 0  # WEAPON_SWORD
	weapon.base_damage = 15.0
	weapon.damage_type = 0  # PHYSICAL
	
	print("Created weapon: ", weapon.item_name)
	print("Weapon damage: ", weapon.base_damage)
	print("Weapon type: ", weapon.item_type)
	
	# Create armor
	var armor: ArmorResource = ArmorResource.new()
	armor.item_name = "Test Helmet"
	armor.item_type = 10  # ARMOR_HELMET
	armor.armor_value = 5.0
	
	print("Created armor: ", armor.item_name)
	print("Armor value: ", armor.armor_value)
	print("Armor type: ", armor.item_type)
	
	# Test ItemFactory
	var item_factory_script = preload("res://core/factories/ItemFactory.gd")
	var factory_weapon: WeaponResource = item_factory_script.create_weapon("Factory Sword", 0, 12.0, 0)
	if factory_weapon:
		print("Factory created weapon: ", factory_weapon.item_name)
		print("Factory weapon damage: ", factory_weapon.base_damage)

## Tests the buff system
func _test_buff_system() -> void:
	print("\n--- Testing Buff System ---")
	
	var entity: Node3D = Node3D.new()
	add_child(entity)
	
	var buffs: BuffComponent = BuffComponent.new()
	entity.add_child(buffs)
	
	# Wait a frame for component to initialize
	await get_tree().process_frame
	
	# Apply some buffs
	buffs.apply_multiplier_buff("Attack Boost", "attack", 1.5, 5.0)
	buffs.apply_flat_buff("Damage Bonus", "attack", 10.0, 3.0)
	
	print("Applied buffs")
	print("Attack multiplier: ", buffs.get_stat_multiplier("attack"))
	print("Attack flat bonus: ", buffs.get_stat_flat_bonus("attack"))
	print("Active buffs: ", buffs.get_active_buff_names())
	
	# Clean up
	entity.queue_free()

## Simple input handling for manual testing
func _input(event: InputEvent) -> void:
	if event.is_action_pressed("ui_accept"):
		print("\n--- Manual Test Triggered ---")
		_test_basic_combat()
