## Resource class for weapon data
## Follows the Resource pattern for data-driven design
## Allows weapons to be created and modified in the Godot editor
class_name WeaponResource
extends Resource

@export_group("Basic Properties")
@export var item_name: String = "Unknown Weapon"
@export var item_type: int = 0  # ItemType.Type.WEAPON_SWORD
@export var description: String = ""
@export var icon: Texture2D

@export_group("Combat Stats")
@export var base_damage: float = 10.0
@export var damage_type: int = 0  # DamageType.Type.PHYSICAL
@export var attack_speed: float = 1.0
@export var critical_chance_bonus: float = 0.0
@export var critical_damage_bonus: float = 0.0

@export_group("Requirements")
@export var required_level: int = 1
@export var required_strength: int = 0
@export var required_dexterity: int = 0
@export var required_intelligence: int = 0

@export_group("Special Properties")
@export var special_effects: Array[String] = []
@export var durability: int = 100
@export var max_durability: int = 100

## Returns the effective damage considering durability
func get_effective_damage() -> float:
	var durability_factor: float = float(durability) / float(max_durability)
	return base_damage * durability_factor

## Returns whether this weapon can be equipped by an entity with given stats
func can_equip(level: int, strength: int, dexterity: int, intelligence: int) -> bool:
	return (level >= required_level and 
			strength >= required_strength and 
			dexterity >= required_dexterity and 
			intelligence >= required_intelligence)

## Reduces durability when weapon is used
func use_weapon() -> void:
	if durability > 0:
		durability -= 1

## Repairs the weapon to full durability
func repair() -> void:
	durability = max_durability

## Returns the weapon's condition as a percentage
func get_condition_percentage() -> float:
	return float(durability) / float(max_durability)
