## Component responsible for managing entity statistics
## Follows Single Responsibility Principle - only handles stats
## Provides clean interface for stat queries and modifications
class_name StatsComponent
extends Node

signal stats_changed(stat_name: String, old_value: float, new_value: float)

@export_group("Base Stats")
@export var level: int = 1
@export var base_health: float = 100.0
@export var base_mana: float = 50.0
@export var base_attack: float = 10.0
@export var base_armor: float = 5.0

@export_group("Attributes")
@export var strength: int = 10
@export var dexterity: int = 10
@export var intelligence: int = 10
@export var vitality: int = 10

@export_group("Combat Stats")
@export var critical_chance: float = 0.05
@export var critical_multiplier: float = 1.5
@export var attack_speed: float = 1.0
@export var movement_speed: float = 100.0

@export_group("Resistances")
@export var damage_resistances: Dictionary = {
	0: 0.0,  # PHYSICAL
	1: 0.0,  # FIRE
	2: 0.0,  # ICE
	3: 0.0,  # LIGHTNING
	4: 0.0,  # POISON
	5: 0.0,  # HOLY
	6: 0.0   # DARK
}

# Cached calculated values for performance
var _cached_max_health: float = -1.0
var _cached_max_mana: float = -1.0
var _cached_total_attack: float = -1.0
var _cached_total_armor: float = -1.0

## Returns the maximum health including attribute bonuses
func get_max_health() -> float:
	if _cached_max_health < 0:
		_cached_max_health = base_health + (vitality * 5.0)
	return _cached_max_health

## Returns the maximum mana including attribute bonuses
func get_max_mana() -> float:
	if _cached_max_mana < 0:
		_cached_max_mana = base_mana + (intelligence * 3.0)
	return _cached_max_mana

## Returns the total attack power including attribute bonuses
func get_total_attack() -> float:
	if _cached_total_attack < 0:
		_cached_total_attack = base_attack + (strength * 2.0) + (dexterity * 1.0)
	return _cached_total_attack

## Returns the total armor including attribute bonuses
func get_total_armor() -> float:
	if _cached_total_armor < 0:
		_cached_total_armor = base_armor + (vitality * 1.0)
	return _cached_total_armor

## Returns the damage resistance for a specific type
func get_damage_resistance(damage_type: int) -> float:
	return damage_resistances.get(damage_type, 0.0)

## Calculates armor damage reduction percentage
func get_armor_reduction() -> float:
	var total_armor: float = get_total_armor()
	return total_armor / (total_armor + 100.0)

## Increases a stat by the specified amount
func increase_stat(stat_name: String, amount: float) -> void:
	var old_value: float = get(stat_name)
	set(stat_name, old_value + amount)
	_invalidate_cache()
	emit_signal("stats_changed", stat_name, old_value, get(stat_name))

## Sets a resistance value for a damage type
func set_damage_resistance(damage_type: int, resistance: float) -> void:
	var clamped_resistance: float = clamp(resistance, 0.0, 0.95)  # Max 95% resistance
	damage_resistances[damage_type] = clamped_resistance

## Invalidates cached values when stats change
func _invalidate_cache() -> void:
	_cached_max_health = -1.0
	_cached_max_mana = -1.0
	_cached_total_attack = -1.0
	_cached_total_armor = -1.0
